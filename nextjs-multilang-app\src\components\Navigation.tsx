"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
// import LanguageSwitcher from "./LanguageSwitcher";

export default function Navigation() {
  const tCommon = useTranslations("Common");
  const tNavigation = useTranslations("Navigation");
  return (
    <nav className="navbar">
      <div className="container">
        <motion.div
          className="nav-logo"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="logo-icon">
            <div className="persona-layers">
              <div className="persona-layer layer-1"></div>
              <div className="persona-layer layer-2"></div>
              <div className="persona-layer layer-3"></div>
            </div>
          </div>
          <span className="logo-text">{tCommon("brandName")}</span>
        </motion.div>

        <motion.div
          className="nav-cta"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* <LanguageSwitcher /> */}
          <button className="btn-primary">{tNavigation("login")}</button>
        </motion.div>
      </div>
    </nav>
  );
}
