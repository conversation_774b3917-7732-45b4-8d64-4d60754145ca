{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_cd76b067._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_75a74226._.js", "server/edge/chunks/[root-of-the-server]__345a0fa9._.js", "server/edge/chunks/edge-wrapper_9191e9f9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh|en|ja))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(zh|en|ja)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CPMI/zEbCknzvIHZWmaMvRokN9Bs87M7zmty+a43oIE=", "__NEXT_PREVIEW_MODE_ID": "f637eb91cbcec0fd0d35e4c9f8354a99", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84c86c7d72e76efb69e47a041d0d95fbfd5c08659eaf77099a33ffaa01535b7a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6ff71ccf0a62f238628545d6f9caf265155ca43639ee552c51a192d23bd6df90"}}}, "sortedMiddleware": ["/"], "functions": {}}