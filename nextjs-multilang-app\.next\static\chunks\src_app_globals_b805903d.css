/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
    }

    ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-500: #fb2c36;
    --color-yellow-400: #fac800;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-cyan-400: #00d2ef;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-purple-500: #ac4bff;
    --color-purple-600: #9810fa;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --blur-xl: 24px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
    --color-text-secondary: var(--text-secondary);
    --gradient-primary: var(--gradient-primary);
    --shadow-soft: var(--shadow-soft);
    --shadow-medium: var(--shadow-medium);
    --shadow-accent: var(--shadow-accent);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-cyan-400: color(display-p3 .294638 .813991 .934996);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-purple-600: color(display-p3 .546729 .130167 .94439);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-cyan-400: lab(76.6045% -40.9406 -29.6231);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-purple-600: lab(43.0295% 75.21 -86.5669);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .visible {
    visibility: visible;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-0\.5 {
    inset: calc(var(--spacing) * .5);
  }

  .inset-1 {
    inset: calc(var(--spacing) * 1);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-8 {
    top: calc(var(--spacing) * 8);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-9 {
    left: calc(var(--spacing) * 9);
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-full {
    height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-52 {
    width: calc(var(--spacing) * 52);
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .flex-1 {
    flex: 1;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-20 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 20) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 20) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-ss-lg:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-ss-lg:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-ss-lg:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-ss-lg:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-ss-lg:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-ss-lg:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-se-lg:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-se-lg:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-se-lg:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-se-lg:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-se-lg:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-se-lg:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: var(--radius-lg);
  }

  .border, .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-\[\#ff6b7a\] {
    border-color: #ff6b7a;
  }

  .border-accent-red {
    border-color: var(--accent-red);
  }

  .border-border {
    border-color: var(--border-color);
  }

  .border-gray-600 {
    border-color: var(--color-gray-600);
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-green-500\/20 {
    border-color: rgba(0, 199, 88, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/20 {
      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .border-purple-500\/20 {
    border-color: rgba(172, 75, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-500\/20 {
      border-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .border-red-500\/20 {
    border-color: rgba(251, 44, 54, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/20 {
      border-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .\!bg-bg-primary {
    background-color: var(--bg-primary) !important;
  }

  .bg-accent-cyan {
    background-color: var(--accent-cyan);
  }

  .bg-accent-purple {
    background-color: var(--accent-purple);
  }

  .bg-accent-red {
    background-color: var(--accent-red);
  }

  .bg-accent-red\/10 {
    background-color: var(--accent-red);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-accent-red\/10 {
      background-color: color-mix(in oklab, var(--accent-red) 10%, transparent);
    }
  }

  .bg-bg-primary {
    background-color: var(--bg-primary);
  }

  .bg-bg-secondary {
    background-color: var(--bg-secondary);
  }

  .bg-bg-tertiary {
    background-color: var(--bg-tertiary);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-gray-800\/50 {
    background-color: rgba(30, 41, 57, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-800\/50 {
      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
    }
  }

  .bg-green-500\/10 {
    background-color: rgba(0, 199, 88, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/10 {
      background-color: color-mix(in oklab, var(--color-green-500) 10%, transparent);
    }
  }

  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-500\/10 {
    --tw-gradient-from: rgba(172, 75, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-purple-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .from-purple-500\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-500 {
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-500\/10 {
    --tw-gradient-from: rgba(251, 44, 54, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
  }

  .from-red-500\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-500 {
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-600\/10 {
    --tw-gradient-to: rgba(21, 93, 252, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-600\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-600) 10%, transparent);
    }
  }

  .to-blue-600\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-400 {
    --tw-gradient-to: var(--color-cyan-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600\/10 {
    --tw-gradient-to: rgba(152, 16, 250, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-600\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-600) 10%, transparent);
    }
  }

  .to-purple-600\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .\!px-6 {
    padding-inline: calc(var(--spacing) * 6) !important;
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .\!text-5xl {
    font-size: var(--text-5xl) !important;
    line-height: var(--tw-leading, var(--text-5xl--line-height)) !important;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-accent-red {
    color: var(--accent-red);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-text-muted {
    color: var(--text-muted);
  }

  .text-text-secondary {
    color: var(--text-secondary);
  }

  .text-white {
    color: var(--color-white);
  }

  .placeholder-gray-400::placeholder {
    color: var(--color-gray-400);
  }

  .opacity-40 {
    opacity: .4;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-80 {
    opacity: .8;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:transform:hover {
      transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
    }
  }

  @media (hover: hover) {
    .hover\:border-accent-cyan:hover {
      border-color: var(--accent-cyan);
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-500:hover {
      border-color: var(--color-gray-500);
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-600:hover {
      border-color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-purple-500\/40:hover {
      border-color: rgba(172, 75, 255, .4);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-purple-500\/40:hover {
        border-color: color-mix(in oklab, var(--color-purple-500) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-red-500\/40:hover {
      border-color: rgba(251, 44, 54, .4);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-red-500\/40:hover {
        border-color: color-mix(in oklab, var(--color-red-500) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-bg-secondary:hover {
      background-color: var(--bg-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-bg-tertiary:hover {
      background-color: var(--bg-tertiary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-500:hover {
      color: var(--color-red-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-text-primary:hover {
      color: var(--text-primary);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-red-500\/30:hover {
      --tw-shadow-color: rgba(251, 44, 54, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-red-500\/30:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 30%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .focus\:border-red-500:focus {
    border-color: var(--color-red-500);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-red-500\/20:focus {
    --tw-ring-color: rgba(251, 44, 54, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-red-500\/20:focus {
      --tw-ring-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (min-width: 48rem) {
    .md\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
}

:root {
  --bg-primary: #0a0a0a;
  --bg-secondary: #111;
  --bg-tertiary: #1a1a1a;
  --text-primary: #fff;
  --text-secondary: #a1a1a1;
  --text-muted: #666;
  --accent-red: #ff2e4d;
  --accent-purple: #7c3aed;
  --accent-cyan: #06b6d4;
  --border-color: #222;
  --border-hover: #333;
  --gradient-primary: linear-gradient(135deg, #ff2e4d 0%, #7c3aed 50%, #06b6d4 100%);
  --shadow-soft: 0 4px 20px rgba(0, 0, 0, .3);
  --shadow-medium: 0 8px 40px rgba(0, 0, 0, .4);
  --shadow-accent: 0 10px 30px rgba(255, 46, 77, .2);
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
}

* {
  box-sizing: border-box;
}

html {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  width: 100%;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 16px;
  overflow-x: hidden;
}

@media (max-width: 768px) {
  * {
    max-width: 100vw;
  }

  img, svg, video {
    max-width: 100%;
    height: auto;
  }
}

@keyframes float-1 {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-20px)rotate(2deg);
  }
}

@keyframes float-2 {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-15px)rotate(-1deg);
  }
}

@keyframes float-3 {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-25px)rotate(1deg);
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float-1 {
  animation: 6s ease-in-out infinite float-1;
}

.animate-float-2 {
  animation: 8s ease-in-out infinite float-2;
}

.animate-float-3 {
  animation: 7s ease-in-out infinite float-3;
}

.animate-in {
  animation: .6s ease-out forwards animate-in;
}

.feature-card {
  transition: all .3s;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.screenshot-item {
  opacity: 0;
  transition: all .6s;
  transform: translateY(30px);
}

.screenshot-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.hero {
  box-sizing: border-box;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  min-height: 100vh;
  margin: 0 auto;
  padding: 120px 80px;
  display: grid;
  position: relative;
  overflow: hidden;
}

.hero:before {
  content: "";
  pointer-events: none;
  z-index: 0;
  background: radial-gradient(at top, rgba(255, 46, 77, .1) 0%, rgba(0, 0, 0, 0) 50%);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.hero-content {
  z-index: 2;
  max-width: 600px;
  position: relative;
}

.hero-badge {
  color: var(--accent-red);
  letter-spacing: 1px;
  text-transform: uppercase;
  background: rgba(255, 46, 77, .1);
  border: 1px solid rgba(255, 46, 77, .2);
  border-radius: 50px;
  margin-bottom: 30px;
  padding: 12px 24px;
  font-size: 12px;
  font-weight: 700;
  display: inline-block;
}

.hero-title {
  color: var(--text-primary);
  font-weight: 800;
}

.hero-subtitle {
  color: var(--text-secondary);
  max-width: 500px;
  margin-bottom: 40px;
  font-size: 1.25rem;
  line-height: 1.6;
}

.hero-actions {
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 60px;
  display: flex;
}

.hero-stats {
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 400px;
  display: grid;
}

.stat {
  text-align: center;
}

.stat-number {
  background: var(--gradient-primary);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 8px;
  font-size: 2rem;
  font-weight: 900;
  display: block;
}

.stat-label {
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: .5px;
  font-size: .875rem;
}

.hero-visual {
  z-index: 2;
  height: 500px;
  position: relative;
}

.hero-mockup {
  width: 100%;
  height: 100%;
  position: relative;
}

.floating-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
  border-radius: 12px;
  padding: 20px;
  position: absolute;
}

.floating-card.card-1 {
  top: 20%;
  left: 10%;
}

.floating-card.card-2 {
  top: 10%;
  right: 20%;
}

.floating-card.card-3 {
  bottom: 20%;
  left: 20%;
}

.persona-preview {
  align-items: center;
  gap: 15px;
  display: flex;
}

.persona-preview img {
  border-radius: 50%;
  width: 50px;
  height: 50px;
}

.persona-preview h4 {
  color: var(--text-primary);
  margin-bottom: 4px;
  font-size: 1rem;
  font-weight: 700;
}

.persona-preview p {
  color: var(--text-secondary);
  font-size: .875rem;
}

.platform-icons {
  justify-content: center;
  align-items: center;
  gap: 8px;
  display: flex;
}

.source-icon {
  width: 40px;
  height: 40px;
  color: var(--text-secondary);
  background: rgba(255, 255, 255, .05);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  transition: all .3s;
  display: flex;
}

.source-icon:hover {
  color: var(--accent-red);
  background: rgba(255, 46, 77, .1);
}

.content-preview {
  max-width: 250px;
}

.preview-image {
  margin-bottom: 15px;
}

.content-preview p {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: .875rem;
  line-height: 1.4;
}

.preview-meta {
  color: var(--accent-cyan);
  font-size: .75rem;
  font-weight: 500;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: 4s infinite gradientShift;
}

@keyframes gradientShift {
  0% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }

  100% {
    background-position: 0%;
  }
}

.btn-primary, .btn-secondary {
  cursor: pointer;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: inline-block;
}

.btn-primary {
  background: var(--gradient-primary);
  color: #fff;
  box-shadow: var(--shadow-accent);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 46, 77, .3);
}

.btn-secondary {
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  background: none;
}

.btn-secondary:hover {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, .1);
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 1024px) {
  .hero {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
    padding: 100px 40px;
  }

  .hero-content {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .hero {
    box-sizing: border-box;
    width: 100%;
    max-width: 100vw;
    margin: 0;
    padding: 80px 16px;
    overflow-x: hidden;
  }

  .hero-content {
    text-align: center;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
  }

  .hero-title {
    word-wrap: break-word;
    line-height: 1.2;
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    max-width: 100%;
    font-size: 1.1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
    margin: 0 auto;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
  }

  .hero-actions .btn-large {
    box-sizing: border-box;
    width: 100%;
    max-width: 280px;
  }

  .floating-card {
    box-sizing: border-box;
    max-width: 100%;
    margin-bottom: 20px;
    position: relative !important;
    top: auto !important;
    bottom: auto !important;
    left: auto !important;
    right: auto !important;
  }

  .hero-visual {
    width: 100%;
    height: auto;
    overflow: hidden;
  }

  .hero-mockup {
    width: 100%;
    overflow: hidden;
  }
}

@media (max-width: 480px) {
  .hero {
    max-width: 100vw;
    padding: 60px 12px;
    overflow-x: hidden;
  }

  .hero-title {
    line-height: 1.1;
    font-size: 2rem !important;
  }

  .hero-subtitle {
    max-width: 100%;
    font-size: 1rem;
  }

  .hero-actions .btn-large {
    max-width: 250px;
    padding: 12px 20px;
    font-size: 14px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: .75rem;
  }

  .floating-card {
    margin-bottom: 15px;
    padding: 15px;
  }

  .persona-preview {
    gap: 10px;
  }

  .persona-preview img {
    width: 40px;
    height: 40px;
  }
}

.navbar {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  background: rgba(10, 10, 10, .9);
  padding: 16px 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.navbar .container {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.nav-logo {
  align-items: center;
  gap: 12px;
  display: flex;
}

.logo-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.logo-icon.small {
  width: 32px;
  height: 32px;
}

.persona-layers {
  width: 100%;
  height: 100%;
  position: relative;
}

.persona-layer {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  transition: all .6s cubic-bezier(.4, 0, .2, 1);
  position: absolute;
}

.layer-1 {
  background: linear-gradient(135deg, var(--accent-red), #ff6b7a);
  animation: 8s linear infinite rotate1;
}

.layer-2 {
  background: linear-gradient(135deg, var(--accent-purple), #a855f7);
  opacity: .8;
  animation: 10s linear infinite reverse rotate2;
  transform: scale(.85)rotate(120deg);
}

.layer-3 {
  background: linear-gradient(135deg, var(--accent-cyan), #22d3ee);
  opacity: .6;
  animation: 12s linear infinite rotate3;
  transform: scale(.7)rotate(240deg);
}

@keyframes rotate1 {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate2 {
  from {
    transform: scale(.85)rotate(120deg);
  }

  to {
    transform: scale(.85)rotate(480deg);
  }
}

@keyframes rotate3 {
  from {
    transform: scale(.7)rotate(240deg);
  }

  to {
    transform: scale(.7)rotate(600deg);
  }
}

.logo-text {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 900;
}

.nav-cta {
  align-items: center;
  gap: 16px;
  display: flex;
}

.features {
  background: var(--bg-secondary);
  padding: 100px 0;
  position: relative;
}

.features:before {
  content: "";
  background: var(--bg-secondary);
  z-index: -1;
  margin-left: -50vw;
  margin-right: -50vw;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  right: 50%;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-header h2 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 900;
  line-height: 1.1;
}

.section-header p {
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  font-size: 1.25rem;
}

.features-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  text-align: center;
  opacity: 0;
  border-radius: 16px;
  padding: 40px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(30px);
}

.feature-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.feature-card.featured {
  background: linear-gradient(135deg, rgba(255, 46, 77, .05) 0%, rgba(124, 58, 237, .05) 100%);
  border-color: rgba(255, 46, 77, .2);
}

.feature-card.primary {
  background: linear-gradient(135deg, rgba(255, 46, 77, .1) 0%, rgba(124, 58, 237, .1) 100%);
  border-color: rgba(255, 46, 77, .3);
}

.feature-card:before {
  content: "";
  background: var(--gradient-primary);
  opacity: 0;
  height: 1px;
  transition: opacity .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.feature-card:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-medium);
  transform: translateY(-8px);
}

.feature-card:hover:before {
  opacity: 1;
}

.feature-icon {
  justify-content: center;
  margin-bottom: 30px;
  display: flex;
}

.feature-card h3 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 700;
}

.feature-card p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
}

@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .feature-card {
    padding: 30px;
  }
}

.cta {
  text-align: center;
  background: var(--bg-secondary);
  padding: 100px 0;
  position: relative;
}

.cta:before {
  content: "";
  background: var(--bg-secondary);
  z-index: -1;
  margin-left: -50vw;
  margin-right: -50vw;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  right: 50%;
}

.cta-content {
  text-align: center;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.cta-content h2 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1.1;
}

.cta-content > p {
  color: var(--text-secondary);
  margin-bottom: 50px;
  font-size: 1.25rem;
  line-height: 1.6;
}

.cta-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  gap: 15px;
  margin-bottom: 20px;
  display: flex;
}

.email-input {
  border: 2px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: 12px;
  flex: 1;
  padding: 18px 24px;
  font-size: 1rem;
  transition: all .3s;
}

.email-input:focus {
  border-color: var(--accent-red);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 46, 77, .1);
}

.email-input::placeholder {
  color: var(--text-secondary);
}

.btn-large {
  white-space: nowrap;
  padding: 14px 32px;
  font-size: 1rem;
  font-weight: 600;
}

.form-note {
  color: var(--text-secondary);
  margin-top: 15px;
  font-size: .9rem;
}

.success-message {
  text-align: center;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  max-width: 400px;
  margin: 0 auto;
  padding: 40px;
}

.success-icon {
  margin-bottom: 20px;
  font-size: 3rem;
}

.success-message h3 {
  color: var(--text-primary);
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: 700;
}

.success-message p {
  color: var(--text-secondary);
  margin: 0;
}

@media (max-width: 768px) {
  .cta {
    padding: 80px 0;
  }

  .cta-content h2 {
    font-size: 2.5rem;
  }

  .form-group {
    flex-direction: column;
    gap: 15px;
  }

  .btn-large {
    width: 100%;
  }
}

.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 80px 0 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
  display: grid;
}

.footer-brand {
  max-width: 400px;
}

.brand-description {
  color: var(--text-secondary);
  margin-top: 20px;
  line-height: 1.6;
}

.footer-links {
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  display: grid;
}

.link-group h4 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 1rem;
  font-weight: 600;
}

.link-group ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.link-group li {
  margin-bottom: 12px;
}

.link-group a {
  color: var(--text-secondary);
  font-size: .9rem;
  text-decoration: none;
  transition: color .3s;
}

.link-group a:hover {
  color: var(--text-primary);
}

.footer-bottom {
  border-top: 1px solid var(--border-color);
  justify-content: space-between;
  align-items: center;
  padding-top: 40px;
  display: flex;
}

.footer-legal {
  align-items: center;
  gap: 30px;
  display: flex;
}

.footer-legal p {
  color: var(--text-secondary);
  margin: 0;
  font-size: .9rem;
}

.legal-links {
  gap: 20px;
  display: flex;
}

.legal-links a {
  color: var(--text-secondary);
  font-size: .9rem;
  text-decoration: none;
  transition: color .3s;
}

.legal-links a:hover {
  color: var(--text-primary);
}

.footer-social {
  gap: 15px;
  display: flex;
}

.social-link {
  border: 1px solid var(--border-color);
  width: 40px;
  height: 40px;
  color: var(--text-secondary);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.social-link:hover {
  color: var(--text-primary);
  border-color: var(--accent-red);
  background: var(--accent-red) / 10;
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 30px;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .footer-bottom {
    text-align: center;
    flex-direction: column;
    gap: 20px;
  }

  .footer-legal {
    flex-direction: column;
    gap: 15px;
  }

  .legal-links {
    justify-content: center;
  }
}

.demo {
  background: var(--bg-primary);
  padding: 120px 0;
}

.demo-grid {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.demo-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  transition: all .3s;
  overflow: hidden;
}

.demo-card:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--border-hover);
  transform: translateY(-4px);
}

.demo-header {
  border-bottom: 1px solid var(--border-color);
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  display: flex;
}

.demo-header h3 {
  color: var(--text-primary);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.demo-status {
  background: var(--accent-red) / 10;
  color: var(--accent-red);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: .8rem;
  font-weight: 500;
}

.demo-content {
  padding: 24px;
}

.photo-grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 20px;
  display: grid;
}

.photo-item {
  aspect-ratio: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  display: flex;
}

.analysis-results {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.insight {
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  display: flex;
}

.insight-label {
  color: var(--text-secondary);
  font-size: .9rem;
}

.insight-value {
  color: var(--text-primary);
  font-size: .9rem;
  font-weight: 500;
}

.persona-list {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.persona-item {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  gap: 16px;
  padding: 16px;
  transition: all .3s;
  display: flex;
}

.persona-item.active {
  border-color: var(--accent-red);
  background: var(--accent-red) / 5;
}

.persona-item:hover {
  border-color: var(--border-hover);
}

.persona-avatar {
  background: var(--bg-primary);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  font-size: 1.5rem;
  display: flex;
}

.persona-info h4 {
  color: var(--text-primary);
  margin: 0 0 4px;
  font-size: 1rem;
  font-weight: 600;
}

.persona-info p {
  color: var(--text-secondary);
  margin: 0;
  font-size: .85rem;
  line-height: 1.4;
}

.generated-post {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.post-header {
  border-bottom: 1px solid var(--border-color);
  align-items: center;
  gap: 12px;
  padding: 16px;
  display: flex;
}

.post-avatar {
  background: var(--bg-primary);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  display: flex;
}

.post-info h4 {
  color: var(--text-primary);
  margin: 0;
  font-size: .9rem;
  font-weight: 600;
}

.post-info span {
  color: var(--text-secondary);
  font-size: .8rem;
}

.post-content {
  padding: 16px;
}

.post-content p {
  color: var(--text-primary);
  margin: 0 0 16px;
  font-size: .9rem;
  line-height: 1.5;
}

.post-stats {
  gap: 20px;
  display: flex;
}

.post-stats span {
  color: var(--text-secondary);
  font-size: .8rem;
}

@media (max-width: 768px) {
  .demo {
    padding: 80px 0;
  }

  .demo-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .photo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.icon-personas {
  width: 100%;
  height: 100%;
  position: relative;
}

.mini-persona {
  border: 2px solid var(--bg-primary);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  position: absolute;
}

.mini-persona.p1 {
  background: var(--accent-red);
  top: 0;
  left: 20px;
}

.mini-persona.p2 {
  background: var(--accent-purple);
  top: 20px;
  left: 0;
}

.mini-persona.p3 {
  background: var(--accent-cyan);
  top: 20px;
  right: 0;
}

.icon-camera {
  background: var(--bg-secondary);
  border: 2px solid var(--accent-cyan);
  border-radius: 8px;
  width: 50px;
  height: 40px;
  position: relative;
}

.camera-lens {
  background: var(--accent-cyan);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.camera-flash {
  background: var(--accent-red);
  border-radius: 2px;
  width: 12px;
  height: 12px;
  position: absolute;
  top: -8px;
  right: -8px;
}

.icon-trend {
  width: 60px;
  height: 60px;
  position: relative;
}

.trend-line {
  background: var(--gradient-primary);
  transform-origin: 0;
  height: 2px;
  position: absolute;
}

.trend-line.line-1 {
  width: 30px;
  top: 40%;
  left: 5px;
  transform: rotate(-20deg);
}

.trend-line.line-2 {
  width: 25px;
  top: 50%;
  left: 20px;
  transform: rotate(15deg);
}

.trend-line.line-3 {
  width: 20px;
  top: 45%;
  right: 10px;
  transform: rotate(-10deg);
}

.trend-dot {
  background: var(--accent-red);
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: absolute;
}

.trend-dot.dot-1 {
  top: 28px;
  left: 8px;
}

.trend-dot.dot-2 {
  background: var(--accent-purple);
  top: 18px;
  left: 30px;
}

.trend-dot.dot-3 {
  background: var(--accent-cyan);
  top: 22px;
  right: 8px;
}

.icon-knowledge {
  width: 60px;
  height: 60px;
  position: relative;
}

.knowledge-sources {
  width: 100%;
  height: 100%;
  position: relative;
}

.source-node {
  border: 2px solid;
  border-radius: 4px;
  width: 16px;
  height: 16px;
  position: absolute;
}

.source-node.doc {
  background: var(--accent-red);
  border-color: var(--accent-red);
  top: 10px;
  left: 10px;
}

.source-node.link {
  background: var(--accent-purple);
  border-color: var(--accent-purple);
  top: 10px;
  right: 10px;
}

.source-node.feed {
  background: var(--accent-cyan);
  border-color: var(--accent-cyan);
  bottom: 10px;
  left: 10px;
}

.knowledge-center {
  background: var(--bg-primary);
  border: 2px solid var(--text-primary);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.icon-viral {
  width: 60px;
  height: 60px;
  position: relative;
}

.viral-bubble {
  border: 2px solid var(--accent-red);
  border-radius: 50%;
  position: absolute;
}

.viral-bubble.bubble-1 {
  width: 20px;
  height: 20px;
  top: 20px;
  left: 20px;
}

.viral-bubble.bubble-2 {
  border-color: var(--accent-purple);
  width: 15px;
  height: 15px;
  top: 10px;
  right: 15px;
}

.viral-bubble.bubble-3 {
  border-color: var(--accent-cyan);
  width: 18px;
  height: 18px;
  bottom: 10px;
  left: 10px;
}

.viral-pulse {
  background: var(--accent-red);
  opacity: .2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: 2s ease-in-out infinite pulse;
  position: absolute;
  top: 10px;
  left: 10px;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes pulse-ring {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%)scale(1);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%)scale(2);
  }
}

.icon-analytics {
  width: 60px;
  height: 60px;
  position: relative;
}

.chart-bar {
  background: var(--gradient-primary);
  border-radius: 4px 4px 0 0;
  width: 8px;
  position: absolute;
  bottom: 10px;
}

.chart-bar.bar-1 {
  height: 15px;
  left: 10px;
}

.chart-bar.bar-2 {
  background: var(--accent-purple);
  height: 25px;
  left: 20px;
}

.chart-bar.bar-3 {
  background: var(--accent-cyan);
  height: 20px;
  left: 30px;
}

.chart-bar.bar-4 {
  background: var(--accent-red);
  height: 30px;
  left: 40px;
}

.chart-line {
  background: var(--accent-red);
  width: 45px;
  height: 2px;
  position: absolute;
  top: 15px;
  left: 8px;
  transform: rotate(-15deg);
}

.chart-line:after {
  content: "";
  background: var(--accent-red);
  border-radius: 50%;
  width: 6px;
  height: 6px;
  position: absolute;
  top: -2px;
  right: -3px;
}

.feature-title {
  margin-bottom: 16px;
  font-size: 24px;
}

.feature-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  color: var(--text-secondary);
  margin-bottom: 8px;
  padding-left: 20px;
  font-size: 14px;
  position: relative;
}

.feature-list li:before {
  content: "✓";
  color: var(--accent-red);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.screenshots {
  padding: 100px 0;
  position: relative;
}

.screenshots:before {
  content: "";
  background: var(--bg-primary);
  z-index: -1;
  margin-left: -50vw;
  margin-right: -50vw;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  right: 50%;
}

.screenshots-showcase {
  flex-direction: column;
  gap: 100px;
  display: flex;
}

.screenshot-item {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  margin: 80px 0;
  display: grid;
}

.screenshot-item.reverse .screenshot-image {
  order: 2;
}

.screenshot-item.reverse .screenshot-info {
  order: 1;
}

.screenshot-image {
  position: relative;
}

.screenshot-info h3 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 2rem;
  font-weight: 700;
}

.screenshot-info p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
}

.demo-camera-roll {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  max-width: 600px;
  margin: 0 auto;
  transition: all .4s;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, .3);
}

.demo-camera-roll:hover {
  border-color: rgba(255, 46, 77, .3);
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(255, 46, 77, .15);
}

.camera-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px;
}

.camera-title-section {
  margin-bottom: 16px;
}

.camera-title {
  color: var(--text-primary);
  margin: 0 0 4px;
  font-size: 18px;
  font-weight: 700;
}

.camera-tip {
  color: var(--text-secondary);
  font-size: 12px;
}

.camera-filter-tabs {
  gap: 12px;
  display: flex;
}

.filter-tab {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  transition: all .3s;
}

.filter-tab.active {
  background: var(--accent-red);
  color: #fff;
  border-color: var(--accent-red);
}

.real-photo-grid {
  background: var(--bg-primary);
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 24px;
  display: grid;
}

.real-photo-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  cursor: pointer;
  border-radius: 16px;
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.real-photo-card:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-cyan);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(6, 182, 212, .2);
}

.real-photo-card.selected {
  border-color: var(--accent-red);
  transform: scale(1.02)translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 46, 77, .25);
}

.photo-image-container {
  height: 120px;
  position: relative;
  overflow: hidden;
}

.photo-placeholder {
  background: linear-gradient(135deg, #333 0%, #555 100%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.photo-placeholder.fashion {
  background: linear-gradient(135deg, #ff6b7a 0%, #c44569 100%);
}

.photo-placeholder.lifestyle {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.photo-placeholder:before {
  content: "📷";
  opacity: .7;
  font-size: 24px;
}

.photo-placeholder.fashion:before {
  content: "👗";
}

.photo-placeholder.lifestyle:before {
  content: "☕";
}

.delete-button {
  opacity: 0;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  background: rgba(239, 68, 68, .9);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  font-size: 14px;
  transition: opacity .3s;
  display: flex;
  position: absolute;
  top: 10px;
  right: 10px;
}

.real-photo-card:hover .delete-button {
  opacity: 1;
}

.photo-info {
  padding: 16px;
}

.photo-filename {
  color: var(--text-secondary);
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
  font-size: 13px;
  font-weight: 600;
  overflow: hidden;
}

.photo-description {
  color: var(--text-muted);
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12px;
  font-size: 12px;
  line-height: 1.5;
  display: -webkit-box;
  overflow: hidden;
}

.photo-meta {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  display: flex;
}

.photo-date {
  color: var(--text-muted);
  font-size: 11px;
}

.photo-status {
  text-transform: uppercase;
  letter-spacing: .5px;
  border-radius: 12px;
  padding: 3px 8px;
  font-size: 11px;
  font-weight: 600;
}

.photo-status.used {
  color: var(--accent-red);
  background: rgba(255, 46, 77, .15);
}

.photo-status.unused {
  background: var(--bg-tertiary);
  color: var(--text-muted);
}

.photo-tags {
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 10px;
  display: flex;
}

.tag {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 8px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
}

.photo-type-badge {
  color: #fff;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, .7);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 10px;
  font-weight: 600;
  position: absolute;
  top: 10px;
  left: 10px;
}

.demo-persona-manager-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, .3);
}

.persona-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 24px;
  display: grid;
}

.persona-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  text-align: center;
  cursor: pointer;
  border-radius: 12px;
  padding: 20px;
  transition: all .3s;
}

.persona-card.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, .05);
  transform: scale(1.02);
}

.persona-card-avatar {
  color: #fff;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  margin: 0 auto 12px;
  font-size: 20px;
  font-weight: 700;
  display: flex;
}

.persona-card-avatar.fiona {
  background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
}

.persona-card-avatar.marcus {
  background: var(--accent-purple);
}

.persona-card h4 {
  color: var(--text-primary);
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 600;
}

.persona-card p {
  color: var(--text-secondary);
  margin: 0 0 12px;
  font-size: 12px;
}

.persona-knowledge-preview {
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 6px;
  width: 120px;
  margin: auto;
  display: flex;
}

.knowledge-chip-mini {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 8px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  display: block;
}

.knowledge-chip-mini.active {
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
  background: rgba(255, 46, 77, .1);
}

.persona-detail-summary {
  background: var(--bg-secondary);
  padding: 24px;
}

.knowledge-sources-summary {
  margin-top: 16px;
}

.knowledge-chips {
  flex-wrap: wrap;
  gap: 8px;
  display: flex;
}

.knowledge-chip {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
}

.knowledge-chip.active {
  color: var(--accent-red);
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, .1);
}

.demo-content-generation-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, .3);
}

.generation-process {
  background: var(--bg-primary);
  padding: 24px;
}

.process-steps {
  justify-content: center;
  align-items: center;
  gap: 16px;
  display: flex;
}

.step-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  min-width: 120px;
  padding: 20px 16px;
  transition: all .3s;
  display: flex;
  position: relative;
}

.step-item.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, .08);
  box-shadow: 0 4px 16px rgba(255, 46, 77, .15);
}

.step-icon {
  background: var(--bg-tertiary);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  font-size: 28px;
  transition: all .3s;
  display: flex;
}

.step-item.active .step-icon {
  background: rgba(255, 46, 77, .15);
  box-shadow: 0 0 0 2px rgba(255, 46, 77, .2);
}

.step-item span {
  color: var(--text-secondary);
  text-align: center;
  font-size: 12px;
  font-weight: 600;
}

.step-item.active span {
  color: var(--text-primary);
}

.step-arrow {
  color: var(--accent-red);
  font-size: 20px;
  font-weight: bold;
}

.generated-content-preview {
  border-top: 1px solid var(--border-color);
  padding: 24px;
}

.content-example {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
}

.content-header {
  gap: 12px;
  margin-bottom: 16px;
  display: flex;
}

.persona-badge {
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
  background: rgba(255, 46, 77, .1);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 600;
}

.trend-badge {
  background: var(--bg-tertiary);
  color: var(--color-text-secondary);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 600;
}

.content-body p {
  color: var(--text-primary);
  margin: 0 0 16px;
  font-size: 14px;
  line-height: 1.5;
}

.content-platforms {
  gap: 8px;
  display: flex;
}

.platform-icon {
  opacity: .7;
  font-size: 16px;
}

@media (max-width: 768px) {
  .screenshots {
    padding: 80px 0;
  }

  .screenshot-item {
    grid-template-columns: 1fr;
    gap: 40px;
    margin: 60px 0;
  }

  .screenshot-item.reverse .screenshot-image {
    order: 1;
  }

  .screenshot-item.reverse .screenshot-info {
    order: 2;
  }

  .screenshot-info h3 {
    font-size: 1.5rem;
  }

  .demo-camera-roll, .demo-persona-manager-compact, .demo-content-generation-compact {
    max-width: 100%;
  }

  .real-photo-grid, .persona-grid {
    grid-template-columns: 1fr;
  }
}

.footer {
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
  padding: 60px 0 30px;
}

.footer-content {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  display: flex;
}

.footer-brand {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.footer-logo {
  align-items: center;
  gap: 12px;
  display: flex;
}

.footer-logo .logo-icon.small {
  width: 32px;
  height: 32px;
}

.footer-logo .logo-text {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 700;
}

.footer-tagline {
  color: var(--text-muted);
  margin: 0;
  font-size: 14px;
}

.footer-links {
  gap: 32px;
  display: flex;
}

.footer-links a {
  color: var(--text-secondary);
  font-size: 14px;
  text-decoration: none;
  transition: color .3s;
}

.footer-links a:hover {
  color: var(--accent-red);
}

.footer-bottom {
  text-align: center;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  padding-top: 30px;
  font-size: 14px;
}

.footer-bottom p {
  margin: 0;
}

@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }

  .footer-content {
    text-align: center;
    flex-direction: column;
    gap: 30px;
  }

  .footer-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }

  .footer-bottom {
    padding-top: 20px;
  }
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=src_app_globals_b805903d.css.map*/