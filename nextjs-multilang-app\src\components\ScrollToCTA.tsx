"use client";

import { useEffect } from "react";

export default function ScrollToCTA() {
  useEffect(() => {
    const handleScrollToCTA = (e: Event) => {
      const target = e.target as HTMLElement;
      
      // Check if the clicked element is a CTA button (but not in a form)
      if (
        target.classList.contains('btn-primary') &&
        (target.textContent?.includes('Google') || target.textContent?.includes('Early Access')) &&
        !target.closest('form')
      ) {
        e.preventDefault();
        
        // Find the CTA section and scroll to it
        const ctaSection = document.querySelector('.cta');
        if (ctaSection) {
          ctaSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // Add event listener to document
    document.addEventListener('click', handleScrollToCTA);

    // Cleanup
    return () => {
      document.removeEventListener('click', handleScrollToCTA);
    };
  }, []);

  return null; // This component doesn't render anything
}
